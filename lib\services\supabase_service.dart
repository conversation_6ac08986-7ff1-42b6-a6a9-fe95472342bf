import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/coffee_shop.dart';

/// Service class that provides access to Supabase client instance
/// 
/// This service acts as a centralized access point for all Supabase operations
/// including authentication, database queries, and real-time subscriptions.
/// 
/// Usage:
/// ```dart
/// final supabaseService = SupabaseService();
/// final client = supabaseService.client;
/// ```
class SupabaseService {
  /// Private constructor to prevent external instantiation
  SupabaseService._();
  
  /// Singleton instance of SupabaseService
  static final SupabaseService _instance = SupabaseService._();
  
  /// Factory constructor that returns the singleton instance
  factory SupabaseService() => _instance;
  
  /// Gets the Supabase client instance
  /// 
  /// Returns the initialized Supabase client that can be used for
  /// database operations, authentication, and real-time subscriptions.
  /// 
  /// Throws [Exception] if Supabase has not been initialized.
  SupabaseClient get client {
    try {
      return Supabase.instance.client;
    } catch (e) {
      throw Exception(
        'Supabase client not initialized. Make sure to call Supabase.initialize() '
        'in main() before using the client.',
      );
    }
  }
  
  /// Checks if Supabase has been initialized
  /// 
  /// Returns `true` if Supabase has been properly initialized, `false` otherwise.
  bool get isInitialized {
    try {
      Supabase.instance.client;
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Gets the current user session
  /// 
  /// Returns the current authenticated user session, or `null` if no user is logged in.
  Session? get currentSession => client.auth.currentSession;
  
  /// Gets the current authenticated user
  /// 
  /// Returns the current authenticated user, or `null` if no user is logged in.
  User? get currentUser => client.auth.currentUser;
  
  /// Checks if a user is currently authenticated
  /// 
  /// Returns `true` if a user is logged in, `false` otherwise.
  bool get isAuthenticated => currentUser != null;
  
  /// Signs out the current user
  /// 
  /// Returns a [Future] that completes when the user has been signed out.
  /// Throws an exception if the sign out operation fails.
  Future<void> signOut() async {
    try {
      await client.auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }
  
  /// Gets a reference to a specific table
  /// 
  /// [tableName] The name of the table to get a reference to.
  /// 
  /// Returns a [SupabaseQueryBuilder] for the specified table.
  /// 
  /// Example:
  /// ```dart
  /// final locations = supabaseService.table('locations');
  /// final data = await locations.select().execute();
  /// ```
  SupabaseQueryBuilder table(String tableName) {
    return client.from(tableName);
  }
  
  /// Gets a reference to Supabase Storage
  /// 
  /// Returns a [SupabaseStorageClient] for file operations.
  SupabaseStorageClient get storage => client.storage;
  
  /// Gets a reference to Supabase Auth
  /// 
  /// Returns a [GoTrueClient] for authentication operations.
  GoTrueClient get auth => client.auth;
  
  /// Gets a reference to Supabase Realtime
  ///
  /// Returns a [RealtimeClient] for real-time subscriptions.
  RealtimeClient get realtime => client.realtime;

  /// Fetches all coffee shops from the 'mekanlar' table
  ///
  /// Returns a [Future] that resolves to a list of [CoffeeShop] objects.
  /// Returns an empty list if no coffee shops are found or if Supabase is not initialized.
  ///
  /// This method includes comprehensive error handling and graceful degradation:
  /// - If Supabase is not initialized, returns an empty list
  /// - If the database query fails, logs the error and returns an empty list
  /// - If JSON parsing fails for individual records, skips those records and continues
  ///
  /// Example usage:
  /// ```dart
  /// final supabaseService = SupabaseService();
  /// final coffeeShops = await supabaseService.getCoffeeShops();
  /// print('Found ${coffeeShops.length} coffee shops');
  /// ```
  Future<List<CoffeeShop>> getCoffeeShops() async {
    try {
      // Check if Supabase is initialized before making the request
      if (!isInitialized) {
        debugPrint('SupabaseService: Supabase not initialized, returning empty list');
        return [];
      }

      debugPrint('SupabaseService: Fetching coffee shops from mekanlar table...');

      // Fetch data from the 'mekanlar' table, ordered by creation date (newest first)
      final response = await client
          .from('mekanlar')
          .select()
          .order('created_at', ascending: false);

      debugPrint('SupabaseService: Received ${response.length} records from database');

      // Convert the response to a list of CoffeeShop objects
      final List<CoffeeShop> coffeeShops = [];

      for (final item in response) {
        try {
          // Attempt to parse each record into a CoffeeShop object
          final coffeeShop = CoffeeShop.fromJson(item);
          coffeeShops.add(coffeeShop);
        } catch (parseError) {
          // Log parsing errors but continue processing other records
          debugPrint('SupabaseService: Failed to parse coffee shop record: $parseError');
          debugPrint('SupabaseService: Problematic record: $item');
          // Skip this record and continue with the next one
          continue;
        }
      }

      debugPrint('SupabaseService: Successfully parsed ${coffeeShops.length} coffee shops');
      return coffeeShops;

    } catch (e) {
      // Log the error for debugging purposes
      debugPrint('SupabaseService: Error fetching coffee shops: $e');

      // Return empty list to allow the app to continue functioning
      // This provides graceful degradation when the database is unavailable
      return [];
    }
  }
}

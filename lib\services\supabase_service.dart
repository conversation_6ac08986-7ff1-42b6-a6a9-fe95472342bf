import 'package:supabase_flutter/supabase_flutter.dart';

/// Service class that provides access to Supabase client instance
/// 
/// This service acts as a centralized access point for all Supabase operations
/// including authentication, database queries, and real-time subscriptions.
/// 
/// Usage:
/// ```dart
/// final supabaseService = SupabaseService();
/// final client = supabaseService.client;
/// ```
class SupabaseService {
  /// Private constructor to prevent external instantiation
  SupabaseService._();
  
  /// Singleton instance of SupabaseService
  static final SupabaseService _instance = SupabaseService._();
  
  /// Factory constructor that returns the singleton instance
  factory SupabaseService() => _instance;
  
  /// Gets the Supabase client instance
  /// 
  /// Returns the initialized Supabase client that can be used for
  /// database operations, authentication, and real-time subscriptions.
  /// 
  /// Throws [Exception] if Supabase has not been initialized.
  SupabaseClient get client {
    try {
      return Supabase.instance.client;
    } catch (e) {
      throw Exception(
        'Supabase client not initialized. Make sure to call Supabase.initialize() '
        'in main() before using the client.',
      );
    }
  }
  
  /// Checks if Supabase has been initialized
  /// 
  /// Returns `true` if Supabase has been properly initialized, `false` otherwise.
  bool get isInitialized {
    try {
      Supabase.instance.client;
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Gets the current user session
  /// 
  /// Returns the current authenticated user session, or `null` if no user is logged in.
  Session? get currentSession => client.auth.currentSession;
  
  /// Gets the current authenticated user
  /// 
  /// Returns the current authenticated user, or `null` if no user is logged in.
  User? get currentUser => client.auth.currentUser;
  
  /// Checks if a user is currently authenticated
  /// 
  /// Returns `true` if a user is logged in, `false` otherwise.
  bool get isAuthenticated => currentUser != null;
  
  /// Signs out the current user
  /// 
  /// Returns a [Future] that completes when the user has been signed out.
  /// Throws an exception if the sign out operation fails.
  Future<void> signOut() async {
    try {
      await client.auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }
  
  /// Gets a reference to a specific table
  /// 
  /// [tableName] The name of the table to get a reference to.
  /// 
  /// Returns a [SupabaseQueryBuilder] for the specified table.
  /// 
  /// Example:
  /// ```dart
  /// final locations = supabaseService.table('locations');
  /// final data = await locations.select().execute();
  /// ```
  SupabaseQueryBuilder table(String tableName) {
    return client.from(tableName);
  }
  
  /// Gets a reference to Supabase Storage
  /// 
  /// Returns a [SupabaseStorageClient] for file operations.
  SupabaseStorageClient get storage => client.storage;
  
  /// Gets a reference to Supabase Auth
  /// 
  /// Returns a [GoTrueClient] for authentication operations.
  GoTrueClient get auth => client.auth;
  
  /// Gets a reference to Supabase Realtime
  /// 
  /// Returns a [RealtimeClient] for real-time subscriptions.
  RealtimeClient get realtime => client.realtime;
}

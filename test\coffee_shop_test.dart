import 'package:flutter_test/flutter_test.dart';
import 'package:siptracker/models/coffee_shop.dart';

void main() {
  group('CoffeeShop Model Tests', () {
    test('should create CoffeeShop from JSON correctly', () {
      // Arrange
      final json = {
        'id': '123e4567-e89b-12d3-a456-426614174000',
        'name': 'Test Coffee Shop',
        'address': '123 Main St, City',
        'latitude': 40.7128,
        'longitude': -74.0060,
        'photo_url': 'https://example.com/photo.jpg',
        'average_rating': 4.5,
        'description': 'A great coffee shop',
        'created_at': '2023-01-01T12:00:00Z',
      };

      // Act
      final coffeeShop = CoffeeShop.fromJson(json);

      // Assert
      expect(coffeeShop.id, '123e4567-e89b-12d3-a456-426614174000');
      expect(coffeeShop.name, 'Test Coffee Shop');
      expect(coffeeShop.address, '123 Main St, City');
      expect(coffeeShop.latitude, 40.7128);
      expect(coffeeShop.longitude, -74.0060);
      expect(coffeeShop.photoUrl, 'https://example.com/photo.jpg');
      expect(coffeeShop.averageRating, 4.5);
      expect(coffeeShop.description, 'A great coffee shop');
      expect(coffeeShop.createdAt, DateTime.parse('2023-01-01T12:00:00Z'));
    });

    test('should handle null values correctly', () {
      // Arrange
      final json = {
        'id': '123e4567-e89b-12d3-a456-426614174000',
        'name': 'Test Coffee Shop',
        'address': '123 Main St, City',
        'latitude': 40.7128,
        'longitude': -74.0060,
        'photo_url': null,
        'average_rating': null,
        'description': null,
        'created_at': '2023-01-01T12:00:00Z',
      };

      // Act
      final coffeeShop = CoffeeShop.fromJson(json);

      // Assert
      expect(coffeeShop.photoUrl, null);
      expect(coffeeShop.averageRating, null);
      expect(coffeeShop.description, null);
    });

    test('should convert to JSON correctly', () {
      // Arrange
      final coffeeShop = CoffeeShop(
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Coffee Shop',
        address: '123 Main St, City',
        latitude: 40.7128,
        longitude: -74.0060,
        photoUrl: 'https://example.com/photo.jpg',
        averageRating: 4.5,
        description: 'A great coffee shop',
        createdAt: DateTime.parse('2023-01-01T12:00:00Z'),
      );

      // Act
      final json = coffeeShop.toJson();

      // Assert
      expect(json['id'], '123e4567-e89b-12d3-a456-426614174000');
      expect(json['name'], 'Test Coffee Shop');
      expect(json['address'], '123 Main St, City');
      expect(json['latitude'], 40.7128);
      expect(json['longitude'], -74.0060);
      expect(json['photo_url'], 'https://example.com/photo.jpg');
      expect(json['average_rating'], 4.5);
      expect(json['description'], 'A great coffee shop');
      expect(json['created_at'], '2023-01-01T12:00:00.000Z');
    });

    test('should handle copyWith correctly', () {
      // Arrange
      final original = CoffeeShop(
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Original Name',
        address: '123 Main St, City',
        latitude: 40.7128,
        longitude: -74.0060,
        createdAt: DateTime.parse('2023-01-01T12:00:00Z'),
      );

      // Act
      final updated = original.copyWith(
        name: 'Updated Name',
        averageRating: 4.8,
      );

      // Assert
      expect(updated.id, original.id);
      expect(updated.name, 'Updated Name');
      expect(updated.address, original.address);
      expect(updated.averageRating, 4.8);
      expect(original.name, 'Original Name'); // Original should be unchanged
    });

    test('should handle equality correctly', () {
      // Arrange
      final coffeeShop1 = CoffeeShop(
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test Coffee Shop',
        address: '123 Main St, City',
        latitude: 40.7128,
        longitude: -74.0060,
        createdAt: DateTime.parse('2023-01-01T12:00:00Z'),
      );

      final coffeeShop2 = CoffeeShop(
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Different Name',
        address: '456 Other St, City',
        latitude: 41.0000,
        longitude: -75.0000,
        createdAt: DateTime.parse('2023-02-01T12:00:00Z'),
      );

      final coffeeShop3 = CoffeeShop(
        id: 'different-id',
        name: 'Test Coffee Shop',
        address: '123 Main St, City',
        latitude: 40.7128,
        longitude: -74.0060,
        createdAt: DateTime.parse('2023-01-01T12:00:00Z'),
      );

      // Act & Assert
      expect(coffeeShop1, coffeeShop2); // Same ID
      expect(coffeeShop1, isNot(coffeeShop3)); // Different ID
      expect(coffeeShop1.hashCode, coffeeShop2.hashCode); // Same hash
    });
  });
}

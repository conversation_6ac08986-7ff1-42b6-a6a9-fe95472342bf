import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../screens/auth_screen.dart';
import 'main_navigation.dart';

/// Authentication gate widget that controls app routing based on authentication state
/// 
/// This widget acts as a routing controller that automatically displays the appropriate
/// screen based on the user's authentication status:
/// - Shows AuthScreen when user is not authenticated
/// - Shows MainNavigation when user is authenticated
/// 
/// The widget listens to global authentication state changes and updates the UI
/// accordingly without requiring manual navigation calls.
class AuthGate extends StatelessWidget {
  const AuthGate({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<User?>(
      valueListenable: currentUser,
      builder: (context, user, child) {
        // Show loading indicator while determining authentication state
        if (user == null && _isCheckingAuth) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  Si<PERSON><PERSON><PERSON>(height: 16),
                  Text('Loading...'),
                ],
              ),
            ),
          );
        }
        
        // Show appropriate screen based on authentication state
        if (user != null) {
          // User is authenticated - show main app
          return const MainNavigation();
        } else {
          // User is not authenticated - show authentication screen
          return const AuthScreen();
        }
      },
    );
  }
}

/// Global authentication state notifier
/// 
/// This ValueNotifier holds the current authenticated user and is updated
/// automatically by the authentication state listener in main.dart.
final ValueNotifier<User?> currentUser = ValueNotifier<User?>(null);

/// Flag to track if we're still checking initial authentication state
bool _isCheckingAuth = true;

/// Updates the global authentication state
/// 
/// This function is called by the authentication state listener to update
/// the global user state when authentication events occur.
void updateAuthState(User? user) {
  currentUser.value = user;
  _isCheckingAuth = false;
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:siptracker/models/coffee_shop.dart';
import 'package:siptracker/screens/coffee_shop_detail_screen.dart';

void main() {
  group('CoffeeShopDetailScreen Tests', () {
    late CoffeeShop testCoffeeShop;

    setUp(() {
      testCoffeeShop = CoffeeShop(
        id: 'test-id-123',
        name: 'Test Coffee Shop',
        address: '123 Test Street, Test City',
        latitude: 40.7128,
        longitude: -74.0060,
        photoUrl: 'https://example.com/test-image.jpg',
        averageRating: 4.5,
        description: 'A wonderful test coffee shop with great coffee.',
        createdAt: DateTime.parse('2023-01-01T12:00:00Z'),
      );
    });

    testWidgets('should display coffee shop information correctly', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: CoffeeShopDetailScreen(coffeeShop: testCoffeeShop),
        ),
      );

      // Wait for any animations to complete
      await tester.pumpAndSettle();

      // Assert - Check if key information is displayed
      expect(find.text('Test Coffee Shop'), findsAtLeastNWidgets(1));
      expect(find.text('123 Test Street, Test City'), findsOneWidget);
      expect(find.text('A wonderful test coffee shop with great coffee.'), findsOneWidget);
      expect(find.text('4.5 / 5.0'), findsOneWidget);
    });

    testWidgets('should display star rating correctly', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: CoffeeShopDetailScreen(coffeeShop: testCoffeeShop),
        ),
      );

      await tester.pumpAndSettle();

      // Assert - Check for star icons (should have stars for 4.5 rating)
      // The exact number depends on the implementation, but there should be star icons
      expect(find.byIcon(Icons.star), findsAtLeastNWidgets(1));
      expect(find.text('4.5 / 5.0'), findsOneWidget);
    });

    testWidgets('should display coordinates information', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: CoffeeShopDetailScreen(coffeeShop: testCoffeeShop),
        ),
      );

      await tester.pumpAndSettle();

      // Assert - Check for coordinate information
      expect(find.text('40.712800'), findsOneWidget);
      expect(find.text('-74.006000'), findsOneWidget);
    });

    testWidgets('should display formatted date correctly', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: CoffeeShopDetailScreen(coffeeShop: testCoffeeShop),
        ),
      );

      await tester.pumpAndSettle();

      // Assert - Check for formatted date
      expect(find.text('January 1, 2023'), findsOneWidget);
    });

    testWidgets('should handle coffee shop without rating', (WidgetTester tester) async {
      // Arrange
      final coffeeShopWithoutRating = CoffeeShop(
        id: 'test-id-456',
        name: 'No Rating Coffee Shop',
        address: '456 No Rating Street',
        latitude: 41.0000,
        longitude: -75.0000,
        createdAt: DateTime.parse('2023-02-01T12:00:00Z'),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: CoffeeShopDetailScreen(coffeeShop: coffeeShopWithoutRating),
        ),
      );

      await tester.pumpAndSettle();

      // Assert - Should not display rating information
      expect(find.text('/ 5.0'), findsNothing);
      expect(find.byIcon(Icons.star), findsNothing);
    });

    testWidgets('should handle coffee shop without description', (WidgetTester tester) async {
      // Arrange
      final coffeeShopWithoutDescription = CoffeeShop(
        id: 'test-id-789',
        name: 'No Description Coffee Shop',
        address: '789 No Description Street',
        latitude: 42.0000,
        longitude: -76.0000,
        createdAt: DateTime.parse('2023-03-01T12:00:00Z'),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: CoffeeShopDetailScreen(coffeeShop: coffeeShopWithoutDescription),
        ),
      );

      await tester.pumpAndSettle();

      // Assert - Should not display About section
      expect(find.text('About'), findsNothing);
    });

    testWidgets('should have floating action button', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: CoffeeShopDetailScreen(coffeeShop: testCoffeeShop),
        ),
      );

      await tester.pumpAndSettle();

      // Assert
      expect(find.byType(FloatingActionButton), findsOneWidget);
      expect(find.byIcon(Icons.favorite_border), findsOneWidget);
    });

    testWidgets('should show snackbar when tapping address', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: CoffeeShopDetailScreen(coffeeShop: testCoffeeShop),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the address section
      final addressCard = find.ancestor(
        of: find.text('123 Test Street, Test City'),
        matching: find.byType(InkWell),
      );
      
      await tester.tap(addressCard);
      await tester.pumpAndSettle();

      // Assert - Should show snackbar
      expect(find.text('Map integration coming soon!'), findsOneWidget);
    });

    testWidgets('should show snackbar when tapping floating action button', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: CoffeeShopDetailScreen(coffeeShop: testCoffeeShop),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the floating action button
      await tester.tap(find.byType(FloatingActionButton));
      await tester.pumpAndSettle();

      // Assert - Should show snackbar
      expect(find.text('Favorites feature coming soon!'), findsOneWidget);
    });
  });
}

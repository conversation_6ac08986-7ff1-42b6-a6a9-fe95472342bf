import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'widgets/auth_gate.dart';

/// Global stream subscription for authentication state changes
StreamSubscription<AuthState>? _authSubscription;

/// Main entry point of the SipTracker application
///
/// Initializes Supabase, sets up authentication state management, and runs the app.
/// Handles initialization errors gracefully to ensure the app works even without
/// proper Supabase configuration.
void main() async {
  // Ensure Flutter binding is initialized before calling async operations
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Supabase with configuration
    // TODO: Move these credentials to environment variables or secure configuration
    // For production, use a secure method to store and retrieve these values
    await Supabase.initialize(
      url: 'YOUR_SUPABASE_URL', // TODO: Replace with actual Supabase project URL
      anonKey: 'YOUR_SUPABASE_ANON_KEY', // TODO: Replace with actual Supabase anon key
    );

    // Set up authentication state listener
    _setupAuthListener();

    // Run the app after successful initialization
    runApp(const SipTrackerApp());
  } catch (e) {
    // Handle Supabase initialization errors
    debugPrint('Failed to initialize Supabase: $e');

    // Update auth state to indicate no authentication available
    updateAuthState(null);

    // Run the app even if Supabase initialization fails
    // This allows the app to function in offline mode or with placeholder data
    runApp(const SipTrackerApp());
  }
}

/// Sets up the authentication state listener
///
/// This function creates a stream subscription to Supabase's authentication
/// state changes and updates the global authentication state accordingly.
/// It handles sign-in, sign-out, and token refresh events automatically.
void _setupAuthListener() {
  try {
    // Get the current user session on app start
    final currentSession = Supabase.instance.client.auth.currentSession;
    updateAuthState(currentSession?.user);

    // Listen to authentication state changes
    _authSubscription = Supabase.instance.client.auth.onAuthStateChange.listen(
      (AuthState authState) {
        debugPrint('Auth state changed: ${authState.event}');

        // Update global authentication state
        updateAuthState(authState.session?.user);
      },
      onError: (error) {
        debugPrint('Auth state listener error: $error');
        // On error, assume user is not authenticated
        updateAuthState(null);
      },
    );
  } catch (e) {
    debugPrint('Failed to set up auth listener: $e');
    // If we can't set up the listener, assume no authentication
    updateAuthState(null);
  }
}

/// Main application widget with authentication-aware routing
///
/// This widget sets up the MaterialApp with the AuthGate as the home widget,
/// which automatically handles routing based on authentication state.
class SipTrackerApp extends StatefulWidget {
  const SipTrackerApp({super.key});

  @override
  State<SipTrackerApp> createState() => _SipTrackerAppState();
}

class _SipTrackerAppState extends State<SipTrackerApp> {
  @override
  void dispose() {
    // Clean up the authentication state listener to prevent memory leaks
    _authSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SipTracker',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const AuthGate(),
      debugShowCheckedModeBanner: false,
    );
  }
}

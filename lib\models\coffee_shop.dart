/// Data model representing a coffee shop location
/// 
/// This model handles data from the Supabase 'mekanlar' table and provides
/// proper type safety and JSON serialization for coffee shop information.
class CoffeeShop {
  /// Unique identifier for the coffee shop (UUID from Supabase)
  final String id;
  
  /// Name of the coffee shop
  final String name;
  
  /// Physical address of the coffee shop
  final String address;
  
  /// Latitude coordinate for map positioning
  final double latitude;
  
  /// Longitude coordinate for map positioning
  final double longitude;
  
  /// Optional URL to the coffee shop's photo
  final String? photoUrl;
  
  /// Optional average rating (0.0 to 5.0)
  final double? averageRating;
  
  /// Optional description of the coffee shop
  final String? description;
  
  /// Timestamp when the record was created
  final DateTime createdAt;

  /// Creates a new CoffeeShop instance
  const CoffeeShop({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    this.photoUrl,
    this.averageRating,
    this.description,
    required this.createdAt,
  });

  /// Creates a CoffeeShop instance from JSON data (typically from Supabase)
  /// 
  /// Handles the conversion of Supabase's snake_case field names to Dart
  /// properties and properly parses the created_at timestamp.
  /// 
  /// Example usage:
  /// ```dart
  /// final coffeeShop = CoffeeShop.fromJson(supabaseResponse);
  /// ```
  factory CoffeeShop.fromJson(Map<String, dynamic> json) {
    return CoffeeShop(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      photoUrl: json['photo_url'] as String?,
      averageRating: json['average_rating'] != null 
          ? (json['average_rating'] as num).toDouble() 
          : null,
      description: json['description'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  /// Converts the CoffeeShop instance to JSON format
  /// 
  /// Useful for sending data back to Supabase or for serialization.
  /// Converts DateTime back to ISO string format expected by Supabase.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'photo_url': photoUrl,
      'average_rating': averageRating,
      'description': description,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// Creates a copy of this CoffeeShop with optionally updated fields
  CoffeeShop copyWith({
    String? id,
    String? name,
    String? address,
    double? latitude,
    double? longitude,
    String? photoUrl,
    double? averageRating,
    String? description,
    DateTime? createdAt,
  }) {
    return CoffeeShop(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      photoUrl: photoUrl ?? this.photoUrl,
      averageRating: averageRating ?? this.averageRating,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'CoffeeShop(id: $id, name: $name, address: $address, '
           'lat: $latitude, lng: $longitude, rating: $averageRating)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CoffeeShop && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

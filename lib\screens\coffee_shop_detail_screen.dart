import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/coffee_shop.dart';

/// Detailed view screen for displaying comprehensive coffee shop information
/// 
/// This screen shows all available information about a selected coffee shop
/// including images, ratings, description, and location details.
/// Follows Material 3 design principles and maintains consistency with the app theme.
class CoffeeShopDetailScreen extends StatefulWidget {
  /// The coffee shop to display details for
  final CoffeeShop coffeeShop;

  /// Creates a coffee shop detail screen
  /// 
  /// [coffeeShop] is required and contains all the information to display
  const CoffeeShopDetailScreen({
    super.key,
    required this.coffeeShop,
  });

  @override
  State<CoffeeShopDetailScreen> createState() => _CoffeeShopDetailScreenState();
}

class _CoffeeShopDetailScreenState extends State<CoffeeShopDetailScreen> {
  /// Scroll controller for managing scroll behavior
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App bar with hero image
          _buildSliverAppBar(),
          // Main content
          SliverToBoxAdapter(
            child: _buildContent(),
          ),
        ],
      ),
      // Floating action button for future features
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// Builds the sliver app bar with hero image
  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 250,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          widget.coffeeShop.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                offset: Offset(0, 1),
                blurRadius: 3,
                color: Colors.black26,
              ),
            ],
          ),
        ),
        background: _buildHeroImage(),
      ),
    );
  }

  /// Builds the hero image section with proper error handling
  Widget _buildHeroImage() {
    return Hero(
      tag: 'coffee_shop_${widget.coffeeShop.id}',
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.3),
            ],
          ),
        ),
        child: widget.coffeeShop.photoUrl != null
            ? Image.network(
                widget.coffeeShop.photoUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildPlaceholderImage();
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                    ),
                  );
                },
              )
            : _buildPlaceholderImage(),
      ),
    );
  }

  /// Builds placeholder image when no photo is available
  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Icon(
        Icons.local_cafe,
        size: 80,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    );
  }

  /// Builds the main content sections
  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Name and rating section
          _buildNameAndRatingSection(),
          const SizedBox(height: 16),
          
          // Address section
          _buildAddressSection(),
          const SizedBox(height: 16),
          
          // Description section (if available)
          if (widget.coffeeShop.description != null) ...[
            _buildDescriptionSection(),
            const SizedBox(height: 16),
          ],
          
          // Additional information section
          _buildAdditionalInfoSection(),
          const SizedBox(height: 16),
          
          // Coordinates section (for debugging/development)
          _buildCoordinatesSection(),
          
          // Bottom spacing for floating action button
          const SizedBox(height: 80),
        ],
      ),
    );
  }

  /// Builds the name and rating section
  Widget _buildNameAndRatingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.coffeeShop.name,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            if (widget.coffeeShop.averageRating != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  ...List.generate(5, (index) {
                    final rating = widget.coffeeShop.averageRating!;
                    if (index < rating.floor()) {
                      return const Icon(Icons.star, color: Colors.amber, size: 20);
                    } else if (index < rating) {
                      return const Icon(Icons.star_half, color: Colors.amber, size: 20);
                    } else {
                      return Icon(Icons.star_border, color: Colors.grey[400], size: 20);
                    }
                  }),
                  const SizedBox(width: 8),
                  Text(
                    '${widget.coffeeShop.averageRating!.toStringAsFixed(1)} / 5.0',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Builds the address section with location icon
  Widget _buildAddressSection() {
    return Card(
      child: InkWell(
        onTap: () {
          // TODO: Future map integration
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Map integration coming soon!'),
              duration: Duration(seconds: 2),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.location_on,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Address',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.coffeeShop.address,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the description section
  Widget _buildDescriptionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'About',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.coffeeShop.description!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds additional information section
  Widget _buildAdditionalInfoSection() {
    final dateFormat = DateFormat('MMMM d, yyyy');
    final formattedDate = dateFormat.format(widget.coffeeShop.createdAt);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.calendar_today,
              label: 'Added on',
              value: formattedDate,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds coordinates section for debugging
  Widget _buildCoordinatesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Location Details',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.my_location,
              label: 'Latitude',
              value: widget.coffeeShop.latitude.toStringAsFixed(6),
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              icon: Icons.my_location,
              label: 'Longitude',
              value: widget.coffeeShop.longitude.toStringAsFixed(6),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds an information row with icon, label, and value
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// Builds floating action button for future features
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () {
        // TODO: Add favorite functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Favorites feature coming soon!'),
            duration: Duration(seconds: 2),
          ),
        );
      },
      child: const Icon(Icons.favorite_border),
    );
  }
}

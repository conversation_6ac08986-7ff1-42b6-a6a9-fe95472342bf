import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/coffee_shop.dart';
import '../models/review.dart';
import '../services/supabase_service.dart';

/// Detailed view screen for displaying comprehensive coffee shop information
/// 
/// This screen shows all available information about a selected coffee shop
/// including images, ratings, description, and location details.
/// Follows Material 3 design principles and maintains consistency with the app theme.
class CoffeeShopDetailScreen extends StatefulWidget {
  /// The coffee shop to display details for
  final CoffeeShop coffeeShop;

  /// Creates a coffee shop detail screen
  /// 
  /// [coffeeShop] is required and contains all the information to display
  const CoffeeShopDetailScreen({
    super.key,
    required this.coffeeShop,
  });

  @override
  State<CoffeeShopDetailScreen> createState() => _CoffeeShopDetailScreenState();
}

class _CoffeeShopDetailScreenState extends State<CoffeeShopDetailScreen> {
  /// Scroll controller for managing scroll behavior
  final ScrollController _scrollController = ScrollController();

  /// Supabase service instance for review operations
  final SupabaseService _supabaseService = SupabaseService();

  /// State variables for review management
  List<Review> _reviews = [];
  bool _reviewsLoading = true;
  String? _reviewsError;

  @override
  void initState() {
    super.initState();
    _loadReviews();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Loads reviews for the current coffee shop
  Future<void> _loadReviews() async {
    try {
      setState(() {
        _reviewsLoading = true;
        _reviewsError = null;
      });

      final reviews = await _supabaseService.getReviewsForShop(widget.coffeeShop.id);

      if (mounted) {
        setState(() {
          _reviews = reviews;
          _reviewsLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _reviewsError = 'Failed to load reviews: $e';
          _reviewsLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App bar with hero image
          _buildSliverAppBar(),
          // Main content
          SliverToBoxAdapter(
            child: _buildContent(),
          ),
        ],
      ),
      // Floating action button for future features
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// Builds the sliver app bar with hero image
  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 250,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          widget.coffeeShop.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                offset: Offset(0, 1),
                blurRadius: 3,
                color: Colors.black26,
              ),
            ],
          ),
        ),
        background: _buildHeroImage(),
      ),
    );
  }

  /// Builds the hero image section with proper error handling
  Widget _buildHeroImage() {
    return Hero(
      tag: 'coffee_shop_${widget.coffeeShop.id}',
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.3),
            ],
          ),
        ),
        child: widget.coffeeShop.photoUrl != null
            ? Image.network(
                widget.coffeeShop.photoUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildPlaceholderImage();
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                    ),
                  );
                },
              )
            : _buildPlaceholderImage(),
      ),
    );
  }

  /// Builds placeholder image when no photo is available
  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Icon(
        Icons.local_cafe,
        size: 80,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    );
  }

  /// Builds the main content sections
  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Name and rating section
          _buildNameAndRatingSection(),
          const SizedBox(height: 16),
          
          // Address section
          _buildAddressSection(),
          const SizedBox(height: 16),
          
          // Description section (if available)
          if (widget.coffeeShop.description != null) ...[
            _buildDescriptionSection(),
            const SizedBox(height: 16),
          ],
          
          // Additional information section
          _buildAdditionalInfoSection(),
          const SizedBox(height: 16),

          // Reviews section
          _buildReviewsSection(),
          const SizedBox(height: 16),

          // Coordinates section (for debugging/development)
          _buildCoordinatesSection(),
          
          // Bottom spacing for floating action button
          const SizedBox(height: 80),
        ],
      ),
    );
  }

  /// Builds the name and rating section
  Widget _buildNameAndRatingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.coffeeShop.name,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            if (widget.coffeeShop.averageRating != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  ...List.generate(5, (index) {
                    final rating = widget.coffeeShop.averageRating!;
                    if (index < rating.floor()) {
                      return const Icon(Icons.star, color: Colors.amber, size: 20);
                    } else if (index < rating) {
                      return const Icon(Icons.star_half, color: Colors.amber, size: 20);
                    } else {
                      return Icon(Icons.star_border, color: Colors.grey[400], size: 20);
                    }
                  }),
                  const SizedBox(width: 8),
                  Text(
                    '${widget.coffeeShop.averageRating!.toStringAsFixed(1)} / 5.0',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Builds the address section with location icon
  Widget _buildAddressSection() {
    return Card(
      child: InkWell(
        onTap: () {
          // TODO: Future map integration
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Map integration coming soon!'),
              duration: Duration(seconds: 2),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.location_on,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Address',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.coffeeShop.address,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the description section
  Widget _buildDescriptionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'About',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.coffeeShop.description!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds additional information section
  Widget _buildAdditionalInfoSection() {
    final dateFormat = DateFormat('MMMM d, yyyy');
    final formattedDate = dateFormat.format(widget.coffeeShop.createdAt);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.calendar_today,
              label: 'Added on',
              value: formattedDate,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the reviews section with list of reviews and add review functionality
  Widget _buildReviewsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header with review count
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Reviews (${_reviews.length})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                // Add review button
                ElevatedButton.icon(
                  onPressed: _showAddReviewModal,
                  icon: const Icon(Icons.add, size: 18),
                  label: const Text('Add Review'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Reviews content
            _buildReviewsContent(),
          ],
        ),
      ),
    );
  }

  /// Builds the reviews content based on current state
  Widget _buildReviewsContent() {
    // Show loading indicator
    if (_reviewsLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(24),
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show error message with retry option
    if (_reviewsError != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 8),
              Text(
                'Failed to load reviews',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 4),
              Text(
                _reviewsError!,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _loadReviews,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    // Show empty state if no reviews
    if (_reviews.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Icon(
                Icons.rate_review_outlined,
                size: 48,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(height: 8),
              Text(
                'No reviews yet',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 4),
              Text(
                'Be the first to review this coffee shop!',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Show reviews list
    return Column(
      children: _reviews.map((review) => _buildReviewCard(review)).toList(),
    );
  }

  /// Builds a single review card
  Widget _buildReviewCard(Review review) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User info and rating row
          Row(
            children: [
              // User avatar
              CircleAvatar(
                radius: 16,
                backgroundColor: Theme.of(context).colorScheme.primary,
                child: Text(
                  review.displayName.isNotEmpty ? review.displayName[0].toUpperCase() : 'A',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              // User name and date
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review.displayName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      _formatReviewDate(review.createdAt),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              // Star rating
              Row(
                children: List.generate(5, (index) {
                  return Icon(
                    index < review.rating ? Icons.star : Icons.star_border,
                    size: 16,
                    color: index < review.rating ? Colors.amber : Colors.grey[400],
                  );
                }),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Review comment
          Text(
            review.comment,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  /// Formats review date for display
  String _formatReviewDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes} minutes ago';
      }
      return '${difference.inHours} hours ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return DateFormat('MMM d, yyyy').format(date);
    }
  }

  /// Builds coordinates section for debugging
  Widget _buildCoordinatesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Location Details',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              icon: Icons.my_location,
              label: 'Latitude',
              value: widget.coffeeShop.latitude.toStringAsFixed(6),
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              icon: Icons.my_location,
              label: 'Longitude',
              value: widget.coffeeShop.longitude.toStringAsFixed(6),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds an information row with icon, label, and value
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// Shows the add review modal bottom sheet
  void _showAddReviewModal() {
    // Check if user is authenticated
    if (!_supabaseService.isAuthenticated) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please sign in to add a review'),
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _AddReviewModal(
        coffeeShop: widget.coffeeShop,
        onReviewAdded: () {
          // Refresh reviews list when a new review is added
          _loadReviews();
        },
      ),
    );
  }

  /// Builds floating action button for future features
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () {
        // TODO: Add favorite functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Favorites feature coming soon!'),
            duration: Duration(seconds: 2),
          ),
        );
      },
      child: const Icon(Icons.favorite_border),
    );
  }
}

/// Modal bottom sheet for adding a new review
class _AddReviewModal extends StatefulWidget {
  final CoffeeShop coffeeShop;
  final VoidCallback onReviewAdded;

  const _AddReviewModal({
    required this.coffeeShop,
    required this.onReviewAdded,
  });

  @override
  State<_AddReviewModal> createState() => _AddReviewModalState();
}

class _AddReviewModalState extends State<_AddReviewModal> {
  final _formKey = GlobalKey<FormState>();
  final _commentController = TextEditingController();
  final _supabaseService = SupabaseService();

  int _selectedRating = 0;
  bool _isSubmitting = false;
  String? _errorMessage;

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Add Review',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Share your experience at ${widget.coffeeShop.name}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 24),

            // Rating selector
            Text(
              'Rating',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: List.generate(5, (index) {
                final starIndex = index + 1;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedRating = starIndex;
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: Icon(
                      starIndex <= _selectedRating ? Icons.star : Icons.star_border,
                      size: 32,
                      color: starIndex <= _selectedRating ? Colors.amber : Colors.grey[400],
                    ),
                  ),
                );
              }),
            ),
            const SizedBox(height: 24),

            // Comment field
            Form(
              key: _formKey,
              child: TextFormField(
                controller: _commentController,
                maxLines: 4,
                maxLength: 500,
                decoration: InputDecoration(
                  labelText: 'Your Review',
                  hintText: 'Tell others about your experience...',
                  border: const OutlineInputBorder(),
                  alignLabelWithHint: true,
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your review';
                  }
                  return null;
                },
              ),
            ),

            // Error message
            if (_errorMessage != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Theme.of(context).colorScheme.onErrorContainer,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 24),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isSubmitting ? null : () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isSubmitting || _selectedRating == 0
                        ? null
                        : _submitReview,
                    child: _isSubmitting
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Submit Review'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submitReview() async {
    if (!_formKey.currentState!.validate() || _selectedRating == 0) {
      return;
    }

    setState(() {
      _isSubmitting = true;
      _errorMessage = null;
    });

    try {
      final currentUser = _supabaseService.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final review = Review.forSubmission(
        shopId: widget.coffeeShop.id,
        userId: currentUser.id,
        rating: _selectedRating,
        comment: _commentController.text.trim(),
        userEmail: currentUser.email,
      );

      await _supabaseService.addReview(review);

      if (mounted) {
        Navigator.pop(context);
        widget.onReviewAdded();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Review added successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString().replaceFirst('Exception: ', '');
          _isSubmitting = false;
        });
      }
    }
  }
}
